# 方程计算器

这是一个用于求解方程 **A + 5X/(B+X) = 75** 的计算器程序，其中A和B是用户输入的已知数，X是待求的未知数。

## 功能特点

- 🧮 **精确计算**：求解一元分式方程
- 🖥️ **图形界面**：使用tkinter构建的友好用户界面
- ✅ **结果验证**：自动验证计算结果的正确性
- ⚠️ **错误处理**：完善的输入验证和异常处理
- 📝 **详细过程**：显示完整的求解步骤

## 方程说明

求解方程：**A + 5X/(B+X) = 75**

### 求解步骤：
1. 移项：5X/(B+X) = 75 - A
2. 两边同乘(B+X)：5X = (75-A)(B+X)
3. 展开：5X = (75-A)B + (75-A)X
4. 移项：5X - (75-A)X = (75-A)B
5. 提取X：X(5-(75-A)) = (75-A)B
6. 化简：X(A-70) = (75-A)B
7. 求解：X = (75-A)B/(A-70)

### 特殊情况：
- 当A = 70时，需要特别处理
- 需要确保B + X ≠ 0（分母不为零）

## 安装和运行

### 1. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python calculator.py
```

## 打包为可执行文件

使用PyInstaller将程序打包为独立的可执行文件：

### 基本打包命令
```bash
pyinstaller --onefile --windowed calculator.py
```

### 高级打包命令（推荐）
```bash
pyinstaller --onefile --windowed --name="方程计算器" --icon=icon.ico calculator.py
```

### 参数说明：
- `--onefile`：打包为单个可执行文件
- `--windowed`：不显示控制台窗口（GUI程序推荐）
- `--name`：指定生成的可执行文件名称
- `--icon`：指定程序图标（需要准备.ico文件）

打包完成后，可执行文件位于 `dist` 目录中。

## 使用方法

1. **启动程序**：运行calculator.py或打包后的可执行文件
2. **输入数值**：
   - 在"A ="框中输入A的值
   - 在"B ="框中输入B的值
3. **计算结果**：点击"计算X"按钮
4. **查看结果**：在结果区域查看详细的计算过程和最终答案
5. **清除数据**：点击"清除"按钮清空所有输入和结果

## 示例

### 示例1：
- 输入：A = 10, B = 5
- 计算过程：X = (75-10)×5/(10-70) = 65×5/(-60) = -5.417
- 验证：10 + 5×(-5.417)/(5+(-5.417)) = 10 + (-27.085)/(-0.417) = 10 + 65 = 75 ✅

### 示例2：
- 输入：A = 70, B = 0
- 结果：特殊情况，X可以是任意值

### 示例3：
- 输入：A = 80, B = 2
- 计算过程：X = (75-80)×2/(80-70) = (-5)×2/10 = -1
- 验证：80 + 5×(-1)/(2+(-1)) = 80 + (-5)/1 = 75 ✅

## 技术特性

- **编程语言**：Python 3.x
- **GUI框架**：tkinter（Python内置）
- **数学计算**：使用标准数学库
- **错误处理**：完善的异常捕获和用户提示
- **界面设计**：响应式布局，支持键盘操作

## 注意事项

1. **输入格式**：支持整数、小数、负数
2. **特殊值**：当A=70时会有特殊处理
3. **无效解**：程序会检测并提示无效解的情况
4. **精度**：计算结果保留足够精度，验证误差小于1e-10

## 故障排除

### 常见问题：
1. **"请输入有效的数字"**：检查输入是否为数字格式
2. **"除零错误"**：检查是否出现了使分母为零的情况
3. **"方程无解"**：某些A、B值组合可能导致方程无解

### 联系支持：
如果遇到问题，请检查：
- Python版本是否为3.6+
- 是否正确激活了虚拟环境
- 依赖是否正确安装

## 版本信息

- **版本**：1.0.0
- **更新日期**：2025-01-28
- **兼容性**：Python 3.6+, Windows/Linux/Mac
