import tkinter as tk
from tkinter import messagebox
import math

class EquationCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("方程计算器 - A + 5X/(B+X) = 75")
        self.root.geometry("450x350")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建界面元素
        self.create_widgets()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建GUI界面元素"""
        # 标题
        title_label = tk.Label(self.root, text="求解方程：A + 5X/(B+X) = 75", 
                              font=("微软雅黑", 16, "bold"), fg="darkblue")
        title_label.pack(pady=15)
        
        # 说明文字
        desc_label = tk.Label(self.root, text="请输入A和B的值，程序将计算X的值", 
                             font=("微软雅黑", 10), fg="gray")
        desc_label.pack(pady=5)
        
        # 输入区域
        input_frame = tk.Frame(self.root)
        input_frame.pack(pady=20)
        
        # A输入
        a_frame = tk.Frame(input_frame)
        a_frame.pack(pady=8)
        tk.Label(a_frame, text="A = ", font=("微软雅黑", 14, "bold")).pack(side=tk.LEFT)
        self.a_entry = tk.Entry(a_frame, font=("Arial", 14), width=20, justify='center')
        self.a_entry.pack(side=tk.LEFT, padx=10)
        
        # B输入
        b_frame = tk.Frame(input_frame)
        b_frame.pack(pady=8)
        tk.Label(b_frame, text="B = ", font=("微软雅黑", 14, "bold")).pack(side=tk.LEFT)
        self.b_entry = tk.Entry(b_frame, font=("Arial", 14), width=20, justify='center')
        self.b_entry.pack(side=tk.LEFT, padx=10)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=15)
        
        calculate_btn = tk.Button(button_frame, text="计算 X", 
                                 command=self.calculate, 
                                 font=("微软雅黑", 12, "bold"), 
                                 bg="lightblue", fg="darkblue",
                                 width=10, height=1)
        calculate_btn.pack(side=tk.LEFT, padx=10)
        
        clear_btn = tk.Button(button_frame, text="清除", 
                             command=self.clear, 
                             font=("微软雅黑", 12), 
                             bg="lightgray", fg="black",
                             width=10, height=1)
        clear_btn.pack(side=tk.LEFT, padx=10)
        
        # 结果显示区域
        result_frame = tk.Frame(self.root)
        result_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        tk.Label(result_frame, text="计算结果：", font=("微软雅黑", 12, "bold")).pack(anchor='w')
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill='both', expand=True)
        
        self.result_text = tk.Text(text_frame, height=8, width=50, 
                                  font=("Consolas", 10), wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill='both', expand=True)
        scrollbar.pack(side=tk.RIGHT, fill='y')
        
        # 绑定回车键
        self.a_entry.bind('<Return>', lambda event: self.calculate())
        self.b_entry.bind('<Return>', lambda event: self.calculate())
    
    def calculate(self):
        """计算X的值"""
        try:
            # 获取并验证输入值
            a_str = self.a_entry.get().strip()
            b_str = self.b_entry.get().strip()
            
            if not a_str or not b_str:
                messagebox.showwarning("输入错误", "请输入A和B的值")
                return
            
            a = float(a_str)
            b = float(b_str)
            
            # 求解方程 A + 5X/(B+X) = 75
            # 整理得：A + 5X/(B+X) = 75
            # 5X/(B+X) = 75 - A
            # 5X = (75 - A)(B + X)
            # 5X = (75 - A)B + (75 - A)X
            # 5X - (75 - A)X = (75 - A)B
            # X(5 - 75 + A) = (75 - A)B
            # X(A - 70) = (75 - A)B
            
            result_text = f"输入值：A = {a}, B = {b}\n"
            result_text += "=" * 40 + "\n"
            
            # 特殊情况处理
            if abs(a - 70) < 1e-10:  # A = 70
                if abs(75 - a) < 1e-10:  # 75 - A = 0
                    result_text += "当A = 70时：\n"
                    result_text += "方程变为：70 + 5X/(B+X) = 75\n"
                    result_text += "即：5X/(B+X) = 5\n"
                    result_text += "即：X/(B+X) = 1\n"
                    result_text += "即：X = B + X\n"
                    result_text += "这意味着：0 = B\n"
                    if abs(b) < 1e-10:
                        result_text += "由于B = 0，X可以是任意值（除了使分母为0的值）\n"
                        result_text += "解：X ∈ R（实数集）"
                    else:
                        result_text += f"由于B = {b} ≠ 0，方程无解"
                else:
                    result_text += "当A = 70时，方程无解\n"
                    result_text += f"因为 75 - A = {75 - a} ≠ 0"
                
                self.show_result(result_text)
                return
            
            # 计算X
            x = (75 - a) * b / (a - 70)
            
            result_text += f"求解过程：\n"
            result_text += f"A + 5X/(B+X) = 75\n"
            result_text += f"{a} + 5X/({b}+X) = 75\n"
            result_text += f"5X/({b}+X) = 75 - {a} = {75-a}\n"
            result_text += f"5X = {75-a} × ({b}+X)\n"
            result_text += f"5X = {(75-a)*b} + {75-a}X\n"
            result_text += f"5X - {75-a}X = {(75-a)*b}\n"
            result_text += f"{5-(75-a)}X = {(75-a)*b}\n"
            result_text += f"{a-70}X = {(75-a)*b}\n"
            result_text += f"X = {(75-a)*b} ÷ {a-70}\n"
            result_text += f"X = {x}\n\n"
            
            # 验证解的有效性（确保B+X≠0）
            if abs(b + x) < 1e-10:
                result_text += "⚠️ 警告：计算得到的X值使得B+X=0，此解无效！\n"
                result_text += f"因为B + X = {b} + {x} = {b+x} ≈ 0"
                self.show_result(result_text)
                return
            
            # 验证解的正确性
            verification = a + 5 * x / (b + x)
            error = abs(verification - 75)
            
            result_text += "验证结果：\n"
            result_text += f"将X = {x}代入原方程：\n"
            result_text += f"A + 5X/(B+X) = {a} + 5×{x}/({b}+{x})\n"
            result_text += f"                = {a} + {5*x}/{b+x}\n"
            result_text += f"                = {a} + {5*x/(b+x)}\n"
            result_text += f"                = {verification}\n\n"
            
            if error < 1e-10:
                result_text += "✅ 验证通过！计算结果正确。\n"
            else:
                result_text += f"⚠️ 验证误差：{error:.2e}\n"
            
            result_text += f"\n最终答案：X = {x}"
            
            self.show_result(result_text)
            
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的数字！\n例如：3.14, -2, 0.5")
        except ZeroDivisionError:
            messagebox.showerror("计算错误", "除零错误！请检查输入值。")
        except Exception as e:
            messagebox.showerror("错误", f"计算过程中出现错误：\n{str(e)}")
    
    def show_result(self, result):
        """显示计算结果"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(1.0, result)
        # 滚动到顶部
        self.result_text.see(1.0)
    
    def clear(self):
        """清除所有输入和结果"""
        self.a_entry.delete(0, tk.END)
        self.b_entry.delete(0, tk.END)
        self.result_text.delete(1.0, tk.END)
        self.a_entry.focus()

def main():
    """主函数"""
    root = tk.Tk()
    app = EquationCalculator(root)
    
    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件可以取消注释
        pass
    except:
        pass
    
    root.mainloop()

if __name__ == "__main__":
    main()
