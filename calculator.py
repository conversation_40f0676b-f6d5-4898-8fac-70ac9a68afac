import tkinter as tk

class EquationCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("方程计算器 - A + 5X/(B+X) = 75")
        self.root.geometry("400x450")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建界面元素
        self.create_widgets()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建GUI界面元素"""
        # 标题
        title_label = tk.Label(self.root, text="求解方程：A + 5X/(B+X) = 75", 
                              font=("微软雅黑", 16, "bold"), fg="darkblue")
        title_label.pack(pady=15)
        
        # 说明文字
        desc_label = tk.Label(self.root, text="请输入A和B的值，程序将计算X的值", 
                             font=("微软雅黑", 10), fg="gray")
        desc_label.pack(pady=5)
        
        # 输入区域
        input_frame = tk.Frame(self.root)
        input_frame.pack(pady=20)
        
        # A输入
        a_frame = tk.Frame(input_frame)
        a_frame.pack(pady=8)
        tk.Label(a_frame, text="A = ", font=("微软雅黑", 14, "bold")).pack(side=tk.LEFT)
        self.a_entry = tk.Entry(a_frame, font=("Arial", 14), width=20, justify='center')
        self.a_entry.pack(side=tk.LEFT, padx=10)
        
        # B输入
        b_frame = tk.Frame(input_frame)
        b_frame.pack(pady=8)
        tk.Label(b_frame, text="B = ", font=("微软雅黑", 14, "bold")).pack(side=tk.LEFT)
        self.b_entry = tk.Entry(b_frame, font=("Arial", 14), width=20, justify='center')
        self.b_entry.pack(side=tk.LEFT, padx=10)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=15)
        
        calculate_btn = tk.Button(button_frame, text="计算 X", 
                                 command=self.calculate, 
                                 font=("微软雅黑", 12, "bold"), 
                                 bg="lightblue", fg="darkblue",
                                 width=10, height=1)
        calculate_btn.pack(side=tk.LEFT, padx=10)
        
        clear_btn = tk.Button(button_frame, text="清除", 
                             command=self.clear, 
                             font=("微软雅黑", 12), 
                             bg="lightgray", fg="black",
                             width=10, height=1)
        clear_btn.pack(side=tk.LEFT, padx=10)
        
        # 结果显示区域
        result_frame = tk.Frame(self.root)
        result_frame.pack(pady=20)

        tk.Label(result_frame, text="计算结果：", font=("微软雅黑", 12, "bold")).pack()

        # 结果标签
        self.result_label = tk.Label(result_frame, text="请输入A和B的值后点击计算",
                                    font=("Arial", 14, "bold"),
                                    fg="blue", bg="lightyellow",
                                    relief="sunken", bd=2,
                                    width=30, height=2)
        self.result_label.pack(pady=10)

        # 绑定回车键
        self.a_entry.bind('<Return>', lambda event: self.calculate())
        self.b_entry.bind('<Return>', lambda event: self.calculate())
    
    def calculate(self):
        """计算X的值"""
        try:
            # 获取并验证输入值
            a_str = self.a_entry.get().strip()
            b_str = self.b_entry.get().strip()

            if not a_str or not b_str:
                self.result_label.config(text="请输入A和B的值", fg="red")
                return

            a = float(a_str)
            b = float(b_str)

            # 特殊情况处理
            if abs(a - 70) < 1e-10:  # A = 70
                if abs(b) < 1e-10:  # B = 0
                    self.result_label.config(text="X 可以是任意实数", fg="blue")
                else:
                    self.result_label.config(text="方程无解", fg="red")
                return

            # 计算X
            x = (75 - a) * b / (a - 70)

            # 验证解的有效性（确保B+X≠0）
            if abs(b + x) < 1e-10:
                self.result_label.config(text="解无效 (分母为零)", fg="red")
                return

            # 显示结果
            if abs(x) < 1e-10:
                result_text = "X = 0"
            elif abs(x - round(x)) < 1e-10:
                result_text = f"X = {int(round(x))}"
            else:
                result_text = f"X = {x:.6f}"

            self.result_label.config(text=result_text, fg="darkgreen")

        except ValueError:
            self.result_label.config(text="请输入有效的数字", fg="red")
        except ZeroDivisionError:
            self.result_label.config(text="除零错误", fg="red")
        except Exception:
            self.result_label.config(text="计算错误", fg="red")

    def clear(self):
        """清除所有输入和结果"""
        self.a_entry.delete(0, tk.END)
        self.b_entry.delete(0, tk.END)
        self.result_label.config(text="请输入A和B的值后点击计算", fg="blue")
        self.a_entry.focus()

def main():
    """主函数"""
    root = tk.Tk()
    EquationCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
